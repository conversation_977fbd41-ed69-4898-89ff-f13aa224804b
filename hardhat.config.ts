import { HardhatUserConfig } from "hardhat/config";
import "@nomicfoundation/hardhat-toolbox";
import "dotenv/config";

const config: HardhatUserConfig = {
  solidity: {
    compilers: [
      {
        version: "0.8.19",
        settings: {
          optimizer: {
            enabled: true,
            runs: 200,
          },
        },
      },
      {
        version: "0.8.20",
        settings: {
          optimizer: {
            enabled: true,
            runs: 200,
          },
        },
      },
    ],
  },
  networks: {
    hardhat: {
      // Isolated testing without forking
      chainId: 31337,
      mining: {
        auto: true,
        interval: 1000, // 1 second block time for fast testing
      },
      accounts: {
        count: 20,
        accountsBalance: "10000000000000000000000", // 10,000 ETH per account
      },
    },
    "hardhat-mainnet-fork": {
      url: "http://localhost:8545",
      chainId: 1,
      forking: {
        url: process.env.MAINNET_FORK_URL || process.env.RPC_URL || "https://eth-mainnet.g.alchemy.com/v2/demo",
        blockNumber: process.env.FORK_BLOCK_NUMBER ? parseInt(process.env.FORK_BLOCK_NUMBER) : undefined,
      },
      mining: {
        auto: true,
        interval: 100, // Fast mining for testing
      },
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
    },
    "hardhat-sepolia-fork": {
      url: "http://localhost:8545",
      chainId: ********,
      forking: {
        url: process.env.SEPOLIA_FORK_URL || "https://eth-sepolia.g.alchemy.com/v2/demo",
        blockNumber: process.env.FORK_BLOCK_NUMBER ? parseInt(process.env.FORK_BLOCK_NUMBER) : undefined,
      },
      mining: {
        auto: true,
        interval: 100, // Fast mining for testing
      },
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
    },
    localhost: {
      url: process.env.RPC_URL || "http://localhost:8545",
      // Don't specify chainId - let it auto-detect from the actual node
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
    },
    "local-mainnet": {
      url: "http://localhost:8545",
      chainId: 1,
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
    },
    "local-sepolia": {
      url: "http://localhost:8545",
      chainId: ********,
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
    },
    sepolia: {
      url: process.env.RPC_URL || "https://eth-sepolia.g.alchemy.com/v2/demo",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      gasPrice: "auto", // Set to auto for dynamic gas price
    },
    mainnet: {
      url: process.env.RPC_URL || "https://eth-mainnet.g.alchemy.com/v2/demo",
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      gasPrice: "auto",
    },
  },
  etherscan: {
    apiKey: process.env.ETHERSCAN_API_KEY || "",
  },
  paths: {
    sources: "./contracts",
    tests: "./test",
    cache: "./cache",
    artifacts: "./artifacts",
  },
  mocha: {
    timeout: 40000,
  },
};

export default config;
