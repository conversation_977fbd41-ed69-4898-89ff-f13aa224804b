#!/bin/bash

# MEV Bot Hardhat Quick Start Script
# This script sets up a complete Hardhat testing environment

set -e  # Exit on any error

echo "🚀 MEV Bot Hardhat Quick Start"
echo "==============================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "hardhat.config.ts" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Step 1: Choose network type
echo "🌐 Choose your testing network:"
echo "1) Mainnet Fork (Realistic testing with real liquidity)"
echo "2) Sepolia Fork (Testnet simulation)"
echo "3) Isolated Network (Clean slate testing)"
echo ""
read -p "Enter your choice (1-3): " network_choice

case $network_choice in
    1)
        FORK_NETWORK="mainnet"
        FORK_COMMAND="npm run hardhat:fork:mainnet"
        print_status "Selected: Mainnet Fork"
        ;;
    2)
        FORK_NETWORK="sepolia"
        FORK_COMMAND="npm run hardhat:fork:sepolia"
        print_status "Selected: Sepolia Fork"
        ;;
    3)
        FORK_NETWORK="none"
        FORK_COMMAND="npm run hardhat:node"
        print_status "Selected: Isolated Network"
        ;;
    *)
        print_error "Invalid choice. Exiting."
        exit 1
        ;;
esac

# Step 2: Check for API key if forking
if [ "$FORK_NETWORK" != "none" ]; then
    if [ -z "$ALCHEMY_API_KEY" ]; then
        print_warning "ALCHEMY_API_KEY environment variable not set"
        read -p "Enter your Alchemy API key (or press Enter to use demo): " api_key
        if [ -n "$api_key" ]; then
            export ALCHEMY_API_KEY="$api_key"
        fi
    fi
fi

# Step 3: Set up environment
print_status "Setting up environment configuration..."
cp .env.hardhat .env

# Update fork network in .env
if [ "$FORK_NETWORK" != "none" ]; then
    sed -i.bak "s/FORK_NETWORK=.*/FORK_NETWORK=$FORK_NETWORK/" .env
    if [ -n "$ALCHEMY_API_KEY" ]; then
        sed -i.bak "s/your_alchemy_api_key_here/$ALCHEMY_API_KEY/g" .env
    fi
fi

print_success "Environment configured for $FORK_NETWORK"

# Step 4: Check if Hardhat node is already running
if curl -s http://localhost:8545 > /dev/null 2>&1; then
    print_warning "Hardhat node already running on localhost:8545"
    read -p "Kill existing node and restart? (y/N): " kill_existing
    if [ "$kill_existing" = "y" ] || [ "$kill_existing" = "Y" ]; then
        print_status "Stopping existing Hardhat node..."
        pkill -f "hardhat node" || true
        sleep 2
    else
        print_status "Using existing Hardhat node"
        SKIP_NODE_START=true
    fi
fi

# Step 5: Start Hardhat node
if [ "$SKIP_NODE_START" != "true" ]; then
    print_status "Starting Hardhat node..."
    echo "Command: $FORK_COMMAND"
    echo ""
    print_warning "Starting Hardhat node in background..."
    print_warning "Check the logs if you encounter issues"
    
    # Start node in background
    nohup $FORK_COMMAND > hardhat-node.log 2>&1 &
    NODE_PID=$!
    
    # Wait for node to start
    print_status "Waiting for Hardhat node to start..."
    for i in {1..30}; do
        if curl -s http://localhost:8545 > /dev/null 2>&1; then
            print_success "Hardhat node started successfully (PID: $NODE_PID)"
            break
        fi
        sleep 1
        if [ $i -eq 30 ]; then
            print_error "Hardhat node failed to start within 30 seconds"
            print_error "Check hardhat-node.log for details"
            exit 1
        fi
    done
fi

# Step 6: Test connection
print_status "Testing network connection..."
if npm run test:hardhat > /dev/null 2>&1; then
    print_success "Network connection test passed"
else
    print_warning "Network connection test failed, but continuing..."
fi

# Step 7: Deploy contracts
print_status "Deploying flashloan contracts..."
if npm run hardhat:deploy; then
    print_success "Contracts deployed successfully"
else
    print_error "Contract deployment failed"
    exit 1
fi

# Step 8: Fund accounts (only for forks)
if [ "$FORK_NETWORK" != "none" ]; then
    print_status "Funding test accounts with tokens..."
    if npm run hardhat:fund; then
        print_success "Accounts funded successfully"
    else
        print_warning "Account funding failed, but continuing..."
    fi
fi

# Step 9: Final setup
print_status "Running final setup checks..."
npm run hardhat:setup

# Success message
echo ""
echo "🎉 Hardhat setup complete!"
echo "=========================="
echo ""
print_success "Your MEV bot testing environment is ready!"
echo ""
echo "📋 What's been set up:"
echo "   ✅ Hardhat node running on localhost:8545"
echo "   ✅ Environment configured for $FORK_NETWORK"
echo "   ✅ Flashloan contracts deployed"
if [ "$FORK_NETWORK" != "none" ]; then
    echo "   ✅ Test accounts funded with tokens"
fi
echo "   ✅ 20 test accounts with 10,000 ETH each"
echo ""
echo "🚀 Next steps:"
echo "   1. Start the MEV bot: npm run dev:hardhat"
echo "   2. Monitor the logs for opportunities"
echo "   3. Test different strategies"
echo ""
echo "🔧 Useful commands:"
echo "   npm run dev:hardhat          # Start MEV bot"
echo "   npm run hardhat:setup        # Test connection"
echo "   npm run test:hardhat         # Run integration tests"
echo "   tail -f hardhat-node.log     # View node logs"
echo ""
echo "🛑 To stop:"
echo "   kill $NODE_PID               # Stop Hardhat node"
echo "   pkill -f 'hardhat node'      # Kill all Hardhat nodes"
echo ""

# Save PID for easy stopping
echo $NODE_PID > .hardhat-node.pid
print_status "Node PID saved to .hardhat-node.pid"

echo "Happy MEV hunting! 🎯"
