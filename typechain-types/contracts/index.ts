/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type * as balancerFlashloanArbitrageSol from "./BalancerFlashloanArbitrage.sol";
export type { balancerFlashloanArbitrageSol };
import type * as hybridFlashloanArbitrageSol from "./HybridFlashloanArbitrage.sol";
export type { hybridFlashloanArbitrageSol };
import type * as mocks from "./mocks";
export type { mocks };
export type { FlashloanArbitrage } from "./FlashloanArbitrage";
export type { SimpleFlashloanArbitrage } from "./SimpleFlashloanArbitrage";
export type { WorkingFlashloanArbitrage } from "./WorkingFlashloanArbitrage";
