/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { ethers } from "ethers";
import {
  DeployContractOptions,
  FactoryOptions,
  HardhatEthersHelpers as HardhatEthersHelpersBase,
} from "@nomicfoundation/hardhat-ethers/types";

import * as Contracts from ".";

declare module "hardhat/types/runtime" {
  interface HardhatEthersHelpers extends HardhatEthersHelpersBase {
    getContractFactory(
      name: "FlashLoanSimpleReceiverBase",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.FlashLoanSimpleReceiverBase__factory>;
    getContractFactory(
      name: "IFlashLoanSimpleReceiver",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IFlashLoanSimpleReceiver__factory>;
    getContractFactory(
      name: "IPool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IPool__factory>;
    getContractFactory(
      name: "IPoolAddressesProvider",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IPoolAddressesProvider__factory>;
    getContractFactory(
      name: "IAuthentication",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IAuthentication__factory>;
    getContractFactory(
      name: "ISignaturesValidator",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ISignaturesValidator__factory>;
    getContractFactory(
      name: "ITemporarilyPausable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ITemporarilyPausable__factory>;
    getContractFactory(
      name: "IWETH",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IWETH__factory>;
    getContractFactory(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20__factory>;
    getContractFactory(
      name: "IAuthorizer",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IAuthorizer__factory>;
    getContractFactory(
      name: "IFlashLoanRecipient",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IFlashLoanRecipient__factory>;
    getContractFactory(
      name: "IProtocolFeesCollector",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IProtocolFeesCollector__factory>;
    getContractFactory(
      name: "IVault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IVault__factory>;
    getContractFactory(
      name: "Ownable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.Ownable__factory>;
    getContractFactory(
      name: "IERC1155Errors",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC1155Errors__factory>;
    getContractFactory(
      name: "IERC20Errors",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20Errors__factory>;
    getContractFactory(
      name: "IERC721Errors",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC721Errors__factory>;
    getContractFactory(
      name: "ERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ERC20__factory>;
    getContractFactory(
      name: "IERC20Metadata",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20Metadata__factory>;
    getContractFactory(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20__factory>;
    getContractFactory(
      name: "SafeCast",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.SafeCast__factory>;
    getContractFactory(
      name: "Strings",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.Strings__factory>;
    getContractFactory(
      name: "BalancerFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.BalancerFlashloanArbitrage__factory>;
    getContractFactory(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV2Router__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "FlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.FlashloanArbitrage__factory>;
    getContractFactory(
      name: "HybridFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.HybridFlashloanArbitrage__factory>;
    getContractFactory(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV2Router__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "MockAavePool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MockAavePool__factory>;
    getContractFactory(
      name: "MockDEXRouter",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MockDEXRouter__factory>;
    getContractFactory(
      name: "MockERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MockERC20__factory>;
    getContractFactory(
      name: "MockPoolAddressesProvider",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MockPoolAddressesProvider__factory>;
    getContractFactory(
      name: "SimpleFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.SimpleFlashloanArbitrage__factory>;
    getContractFactory(
      name: "WorkingFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.WorkingFlashloanArbitrage__factory>;

    getContractAt(
      name: "FlashLoanSimpleReceiverBase",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.FlashLoanSimpleReceiverBase>;
    getContractAt(
      name: "IFlashLoanSimpleReceiver",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IFlashLoanSimpleReceiver>;
    getContractAt(
      name: "IPool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IPool>;
    getContractAt(
      name: "IPoolAddressesProvider",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IPoolAddressesProvider>;
    getContractAt(
      name: "IAuthentication",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IAuthentication>;
    getContractAt(
      name: "ISignaturesValidator",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ISignaturesValidator>;
    getContractAt(
      name: "ITemporarilyPausable",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ITemporarilyPausable>;
    getContractAt(
      name: "IWETH",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IWETH>;
    getContractAt(
      name: "IERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20>;
    getContractAt(
      name: "IAuthorizer",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IAuthorizer>;
    getContractAt(
      name: "IFlashLoanRecipient",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IFlashLoanRecipient>;
    getContractAt(
      name: "IProtocolFeesCollector",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IProtocolFeesCollector>;
    getContractAt(
      name: "IVault",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IVault>;
    getContractAt(
      name: "Ownable",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.Ownable>;
    getContractAt(
      name: "IERC1155Errors",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC1155Errors>;
    getContractAt(
      name: "IERC20Errors",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20Errors>;
    getContractAt(
      name: "IERC721Errors",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC721Errors>;
    getContractAt(
      name: "ERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ERC20>;
    getContractAt(
      name: "IERC20Metadata",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20Metadata>;
    getContractAt(
      name: "IERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20>;
    getContractAt(
      name: "SafeCast",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.SafeCast>;
    getContractAt(
      name: "Strings",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.Strings>;
    getContractAt(
      name: "BalancerFlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.BalancerFlashloanArbitrage>;
    getContractAt(
      name: "IUniswapV2Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV2Router>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "FlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.FlashloanArbitrage>;
    getContractAt(
      name: "HybridFlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.HybridFlashloanArbitrage>;
    getContractAt(
      name: "IUniswapV2Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV2Router>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "MockAavePool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MockAavePool>;
    getContractAt(
      name: "MockDEXRouter",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MockDEXRouter>;
    getContractAt(
      name: "MockERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MockERC20>;
    getContractAt(
      name: "MockPoolAddressesProvider",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MockPoolAddressesProvider>;
    getContractAt(
      name: "SimpleFlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.SimpleFlashloanArbitrage>;
    getContractAt(
      name: "WorkingFlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.WorkingFlashloanArbitrage>;

    deployContract(
      name: "FlashLoanSimpleReceiverBase",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.FlashLoanSimpleReceiverBase>;
    deployContract(
      name: "IFlashLoanSimpleReceiver",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IFlashLoanSimpleReceiver>;
    deployContract(
      name: "IPool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IPool>;
    deployContract(
      name: "IPoolAddressesProvider",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IPoolAddressesProvider>;
    deployContract(
      name: "IAuthentication",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAuthentication>;
    deployContract(
      name: "ISignaturesValidator",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISignaturesValidator>;
    deployContract(
      name: "ITemporarilyPausable",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ITemporarilyPausable>;
    deployContract(
      name: "IWETH",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IWETH>;
    deployContract(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "IAuthorizer",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAuthorizer>;
    deployContract(
      name: "IFlashLoanRecipient",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IFlashLoanRecipient>;
    deployContract(
      name: "IProtocolFeesCollector",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IProtocolFeesCollector>;
    deployContract(
      name: "IVault",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IVault>;
    deployContract(
      name: "Ownable",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.Ownable>;
    deployContract(
      name: "IERC1155Errors",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC1155Errors>;
    deployContract(
      name: "IERC20Errors",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20Errors>;
    deployContract(
      name: "IERC721Errors",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC721Errors>;
    deployContract(
      name: "ERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ERC20>;
    deployContract(
      name: "IERC20Metadata",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20Metadata>;
    deployContract(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "SafeCast",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SafeCast>;
    deployContract(
      name: "Strings",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.Strings>;
    deployContract(
      name: "BalancerFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.BalancerFlashloanArbitrage>;
    deployContract(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "FlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.FlashloanArbitrage>;
    deployContract(
      name: "HybridFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.HybridFlashloanArbitrage>;
    deployContract(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "MockAavePool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockAavePool>;
    deployContract(
      name: "MockDEXRouter",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockDEXRouter>;
    deployContract(
      name: "MockERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockERC20>;
    deployContract(
      name: "MockPoolAddressesProvider",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockPoolAddressesProvider>;
    deployContract(
      name: "SimpleFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SimpleFlashloanArbitrage>;
    deployContract(
      name: "WorkingFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.WorkingFlashloanArbitrage>;

    deployContract(
      name: "FlashLoanSimpleReceiverBase",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.FlashLoanSimpleReceiverBase>;
    deployContract(
      name: "IFlashLoanSimpleReceiver",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IFlashLoanSimpleReceiver>;
    deployContract(
      name: "IPool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IPool>;
    deployContract(
      name: "IPoolAddressesProvider",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IPoolAddressesProvider>;
    deployContract(
      name: "IAuthentication",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAuthentication>;
    deployContract(
      name: "ISignaturesValidator",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISignaturesValidator>;
    deployContract(
      name: "ITemporarilyPausable",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ITemporarilyPausable>;
    deployContract(
      name: "IWETH",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IWETH>;
    deployContract(
      name: "IERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "IAuthorizer",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAuthorizer>;
    deployContract(
      name: "IFlashLoanRecipient",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IFlashLoanRecipient>;
    deployContract(
      name: "IProtocolFeesCollector",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IProtocolFeesCollector>;
    deployContract(
      name: "IVault",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IVault>;
    deployContract(
      name: "Ownable",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.Ownable>;
    deployContract(
      name: "IERC1155Errors",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC1155Errors>;
    deployContract(
      name: "IERC20Errors",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20Errors>;
    deployContract(
      name: "IERC721Errors",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC721Errors>;
    deployContract(
      name: "ERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ERC20>;
    deployContract(
      name: "IERC20Metadata",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20Metadata>;
    deployContract(
      name: "IERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "SafeCast",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SafeCast>;
    deployContract(
      name: "Strings",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.Strings>;
    deployContract(
      name: "BalancerFlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.BalancerFlashloanArbitrage>;
    deployContract(
      name: "IUniswapV2Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "FlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.FlashloanArbitrage>;
    deployContract(
      name: "HybridFlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.HybridFlashloanArbitrage>;
    deployContract(
      name: "IUniswapV2Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "MockAavePool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockAavePool>;
    deployContract(
      name: "MockDEXRouter",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockDEXRouter>;
    deployContract(
      name: "MockERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockERC20>;
    deployContract(
      name: "MockPoolAddressesProvider",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockPoolAddressesProvider>;
    deployContract(
      name: "SimpleFlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SimpleFlashloanArbitrage>;
    deployContract(
      name: "WorkingFlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.WorkingFlashloanArbitrage>;

    // default types
    getContractFactory(
      name: string,
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<ethers.ContractFactory>;
    getContractFactory(
      abi: any[],
      bytecode: ethers.BytesLike,
      signer?: ethers.Signer
    ): Promise<ethers.ContractFactory>;
    getContractAt(
      nameOrAbi: string | any[],
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<ethers.Contract>;
    deployContract(
      name: string,
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<ethers.Contract>;
    deployContract(
      name: string,
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<ethers.Contract>;
  }
}
